import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { MkdLoader } from "@/components/MkdLoader";
import StarIcon from "@/assets/svgs/StarIcon";
import DownloadIcon from "@/assets/svgs/DownloadIcon";
import CheckIcon from "@/assets/svgs/CheckIcon";
import CalendarIcon from "@/assets/svgs/CalendarIcon";
import { ConfirmationCodeCard } from "../../../components/ConfirmationCodeCard";
import { DeliveryStatusTracker } from "../../../components/DeliveryStatusTracker";
import {
  usePickupCodeQuery,
  useDeliveryCodeQuery,
} from "../../../query/useConfirmationCodes";
import { useTransactionQuery } from "../../../query/useTransactions";

interface ITransactionDetails {
  id: string;
  transactionId: string;
  date: string;
  time: string;
  type: string;
  status: string;
  itemName: string;
  itemDescription: string;
  amountPaid: string;
  amountPaidUSD: string;
  buyerName: string;
  buyerLocation: string;
  buyerMemberSince: string;
  buyerVerified: boolean;
  sellerName: string;
  sellerLocation: string;
  sellerType: string;
  sellerVerified: boolean;
  ebaAmount: string;
  usdFee: string;
  totalPaid: string;
  totalPaidUSD: string;
  deliveryStatus: string;
  deliveryDate: string;
  shippingMethod: string;
  shippingDate: string;
  estimatedDelivery: string;
  actualDelivery: string;
  trackingNumber: string;
  confirmationCode: string;
  agentName: string;
  agentPhone: string;
  vehicleNumber: string;
  deliveryAddress: string;
}

const SellerView = ({
  transactionDetails,
  pickupCodeData,
  pickupCodeLoading,
  pickupCodeError,
  refetchPickupCode,
}: {
  transactionDetails: ITransactionDetails;
  pickupCodeData: any;
  pickupCodeLoading: boolean;
  pickupCodeError: any;
  refetchPickupCode: () => void;
}) => {
  return (
    <>
      {/* Amount Earned */}
      <div className="bg-white rounded-lg p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          Amount Earned
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Gross sale</span>
            <span className="font-semibold text-gray-800">
              {transactionDetails.ebaAmount}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Platform fee (10%)</span>
            <span className="font-semibold text-red-500">- eBa$ 34.95</span>
          </div>
          <hr />
          <div className="flex justify-between">
            <span className="font-semibold text-gray-800">Net received</span>
            <span className="font-bold text-gray-800">eBa$ 314.55</span>
          </div>
        </div>
      </div>

      {/* Pickup Confirmation Code */}
      <div className="mb-6">
        <ConfirmationCodeCard
          type="pickup"
          data={pickupCodeData}
          isLoading={pickupCodeLoading}
          error={pickupCodeError}
          onRefresh={refetchPickupCode}
        />
      </div>

      {/* Delivery Tracking */}
      <div className="bg-white rounded-lg p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">
          Delivery Tracking
        </h2>
        <div className="flex items-center mb-6">
          <div className="w-2.5 h-2.5 bg-blue-500 rounded-full mr-3"></div>
          <span className="font-semibold text-blue-600">In Transit</span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <p className="text-sm text-gray-500">Tracking Number</p>
            <p className="font-semibold text-gray-800 mb-4">
              {transactionDetails.trackingNumber.replace("29384756", "7654321")}
            </p>

            <p className="text-sm text-gray-500">Carrier</p>
            <p className="font-semibold text-gray-800 mb-4">eBa Delivery</p>

            <p className="text-sm text-gray-500">Shipping Date</p>
            <p className="font-semibold text-gray-800 mb-4">
              {transactionDetails.shippingDate}
            </p>

            <p className="text-sm text-gray-500">Estimated Delivery</p>
            <p className="font-semibold text-gray-800 mb-4">
              {transactionDetails.estimatedDelivery}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500 mb-2">Delivery Agent</p>
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-full mr-3 bg-gray-300 flex items-center justify-center">
                <span className="text-gray-600 text-sm font-medium">
                  {transactionDetails.agentName.charAt(0)}
                </span>
              </div>
              <div>
                <p className="font-semibold text-gray-800">
                  {transactionDetails.agentName}
                </p>
                <p className="text-sm text-gray-500">
                  {transactionDetails.agentPhone}
                </p>
                <p className="text-sm text-gray-500">
                  Vehicle: {transactionDetails.vehicleNumber}
                </p>
              </div>
            </div>
            <p className="text-sm text-gray-500 mb-2">Buyer Information</p>
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full mr-3 bg-blue-300 flex items-center justify-center">
                <span className="text-blue-700 text-sm font-medium">
                  {transactionDetails.buyerName.charAt(0)}
                </span>
              </div>
              <div>
                <p className="font-semibold text-gray-800">
                  {transactionDetails.buyerName}
                </p>
                <p className="text-sm text-gray-500">
                  {transactionDetails.buyerLocation}
                </p>
                <div className="flex items-center">
                  <div className="flex text-yellow-400">
                    {[...Array(4)].map((_, i) => (
                      <StarIcon key={i} className="w-4 h-4" />
                    ))}
                    <StarIcon className="w-4 h-4 text-gray-300" />
                  </div>
                  <span className="text-sm text-gray-500 ml-2">
                    (28 ratings)
                  </span>
                </div>
                <button className="text-sm text-blue-600 hover:underline">
                  Contact Buyer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const ServiceView = ({
  transactionDetails,
  isSellerCompleted,
  onMarkAsCompleted,
}: {
  transactionDetails: ITransactionDetails;
  isSellerCompleted: boolean;
  onMarkAsCompleted: () => void;
}) => (
  <>
    {/* Buyer and Seller Details */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Buyer Details
        </h3>
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full mr-4 bg-blue-300 flex items-center justify-center">
            <span className="text-blue-700 text-sm font-medium">
              {transactionDetails.buyerName.charAt(0)}
            </span>
          </div>
          <div>
            <p className="font-semibold text-gray-800">
              {transactionDetails.buyerName}
            </p>
            <p className="text-sm text-gray-500">
              Credit Score: 750 - Location: {transactionDetails.buyerLocation}
            </p>
            <p className="text-sm text-gray-500">
              Member since {transactionDetails.buyerMemberSince}{" "}
              <span className="text-green-500">Verified</span>
            </p>
          </div>
        </div>
      </div>
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Seller Details
        </h3>
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full mr-4 bg-green-300 flex items-center justify-center">
            <span className="text-green-700 text-sm font-medium">
              {transactionDetails.sellerName.charAt(0)}
            </span>
          </div>
          <div>
            <p className="font-semibold text-gray-800">
              {transactionDetails.sellerName}
            </p>
            <p className="text-sm text-gray-500">
              4.5 - Location: {transactionDetails.sellerLocation}
            </p>
            <p className="text-sm text-gray-500">
              Service Provider <span className="text-green-500">Verified</span>
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Total Paid */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Total Paid</h3>
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">eBa Amount</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.ebaAmount}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">USD Fee (1%)</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.usdFee}
          </span>
        </div>
        <hr />
        <div className="flex justify-between items-center">
          <span className="font-semibold text-gray-800">Total Paid</span>
          <div className="text-right">
            <p className="font-bold text-lg text-gray-800">
              {transactionDetails.totalPaid}
            </p>
            <p className="text-sm text-gray-500">
              ≈ {transactionDetails.totalPaidUSD}
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Service Completion & Confirmation */}
    {isSellerCompleted ? (
      <div className="bg-white rounded-lg p-6 mb-6">
        <div className="bg-yellow-100 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Seller marked this service as completed on Apr 25, 2025 at 4:15
                PM.
                <br />
                Please confirm if you have received the service.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-6">
          <div>
            <p className="text-sm text-gray-500">Appointment Date</p>
            <div className="flex items-center mt-1">
              <CalendarIcon className="w-5 h-5 text-gray-500 mr-2" />
              <p className="font-semibold text-gray-800">Apr 25, 2025</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Duration</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="font-semibold text-gray-800">2 hours</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Time Slot</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <p className="font-semibold text-gray-800">2:00 PM - 4:00 PM</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Payment Method</p>
            <div className="flex items-center mt-1">
              <svg
                className="w-5 h-5 text-gray-500 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
              <p className="font-semibold text-gray-800">Escrow</p>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={onMarkAsCompleted}
            className="bg-green-500 text-white font-bold py-2 px-6 rounded-lg flex items-center"
          >
            <CheckIcon className="w-5 h-5 mr-2" />
            Mark Service as Completed
          </button>
        </div>
      </div>
    ) : (
      <div className="bg-white rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">
            Service Completion & Confirmation
          </h3>
          <button className="text-sm text-blue-600 hover:underline">
            Refresh Status
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <p className="text-sm text-gray-500 mb-2">Service Details</p>
            <div className="space-y-2">
              <p className="flex items-center">
                <span className="text-green-500 mr-2">✔</span>
                <span className="font-semibold text-gray-800">Completed</span>
                <span className="text-sm text-gray-500 ml-2">
                  Apr 25, 2023 at 16:20 PM
                </span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">
                  Appointment Date:
                </span>
                <span className="text-sm text-gray-600 ml-2">Apr 25, 2023</span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">Time Slot:</span>
                <span className="text-sm text-gray-600 ml-2">
                  2:00 PM - 4:00 PM
                </span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">Duration:</span>
                <span className="text-sm text-gray-600 ml-2">2 hours</span>
              </p>
              <p className="flex items-center">
                <span className="font-semibold text-gray-800">
                  Payment Method:
                </span>
                <span className="text-sm text-gray-600 ml-2">Escrow</span>
              </p>
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm font-semibold text-gray-800 mb-2">
              Service Status
            </p>
            <div className="flex items-start">
              <span className="text-green-500 mr-2">✔</span>
              <p className="text-sm text-gray-600">
                Service marked complete by both parties on Apr 25, 2023 - 16:20.
              </p>
            </div>
            <div className="flex items-start mt-2">
              <span className="text-green-500 mr-2">✔</span>
              <p className="text-sm text-gray-600">
                Payment has been released to the seller.
              </p>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Request Refund */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        Request Refund
      </h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Reason
          </label>
          <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option>Select a reason</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            rows={3}
            placeholder="Describe your issue with the service received"
          ></textarea>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Supporting Evidence
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <p>Drag files here or click to upload</p>
            <p className="text-xs text-gray-500">Max file size: 10MB</p>
          </div>
        </div>
        <div className="flex justify-end">
          <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
            Request Refund
          </button>
        </div>
      </div>
    </div>

    {/* Transaction Timeline */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-6">
        Transaction Timeline
      </h2>
      <ul className="space-y-6">
        {/* Buyer Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <svg
                  className="w-4 h-4 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Buyer Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:20 PM</p>
            <p className="text-sm text-gray-500">
              Service confirmed as completed by buyer
            </p>
          </div>
        </li>
        {/* Seller Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Seller Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:05 PM</p>
            <p className="text-sm text-gray-500">
              Service provider marked the service as completed
            </p>
          </div>
        </li>
        {/* Service Booked */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Service Booked (Confirmed)
            </h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:34 PM</p>
            <p className="text-sm text-gray-500">
              Service appointment confirmed and scheduled
            </p>
          </div>
        </li>
        {/* Payment Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Payment Completed</h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:32 PM</p>
            <p className="text-sm text-gray-500">
              Payment processed successfully
            </p>
          </div>
        </li>
      </ul>
    </div>

    {/* Rate Seller */}
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate Seller</h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your overall experience with the service?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "Professional",
          "On Time",
          "Friendly",
          "Met Expectations",
          "Would Book Again",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the service provider..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
          Submit Rating
        </button>
      </div>
    </div>
  </>
);

const ServiceProviderView = ({
  transactionDetails,
}: {
  transactionDetails: ITransactionDetails;
}) => (
  <>
    {/* Amount Earned */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">
        Amount Earned
      </h2>
      <div className="space-y-3">
        <div className="flex justify-between">
          <span className="text-gray-600">Gross sale</span>
          <span className="font-semibold text-gray-800">
            {transactionDetails.ebaAmount}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Platform fee (10%)</span>
          <span className="font-semibold text-red-500">- eBa$ 34.95</span>
        </div>
        <hr />
        <div className="flex justify-between">
          <span className="font-semibold text-gray-800">Net received</span>
          <span className="font-bold text-gray-800">eBa$ 314.55</span>
        </div>
      </div>
    </div>

    {/* Service Completion & Confirmation */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">
          Service Completion & Confirmation
        </h3>
        <button className="text-sm text-red-500 hover:underline flex items-center">
          Refresh Status
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <p className="text-sm text-gray-500 mb-2">Service Details</p>
          <div className="space-y-2">
            <p className="flex items-center font-semibold text-gray-800">
              <span className="text-green-500 mr-2">✔</span>
              Completed Apr 25, 2023 at 16:20 PM
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">
                Appointment Date:
              </span>
              <span className="text-sm text-gray-600 ml-2">Apr 25, 2023</span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">Time Slot:</span>
              <span className="text-sm text-gray-600 ml-2">
                2:00 PM - 4:00 PM
              </span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">Duration:</span>
              <span className="text-sm text-gray-600 ml-2">2 hours</span>
            </p>
            <p className="flex items-center">
              <span className="font-semibold text-gray-800">
                Payment Method:
              </span>
              <span className="text-sm text-gray-600 ml-2">Escrow</span>
            </p>
          </div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <p className="text-sm font-semibold text-gray-800 mb-2">
            Service Status
          </p>
          <div className="flex items-start">
            <span className="text-green-500 mr-2">✔</span>
            <p className="text-sm text-gray-600">
              Service marked completed by both parties on Apr 25, 2023 - 16:20.
            </p>
          </div>
          <div className="flex items-start mt-2">
            <span className="text-green-500 mr-2">✔</span>
            <p className="text-sm text-gray-600">
              Payment has been released to the seller.
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Transaction Timeline */}
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-6">
        Transaction Timeline
      </h2>
      <ul className="space-y-6">
        {/* Buyer Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                <CheckIcon className="w-4 h-4 text-white" />
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Buyer Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:20 PM</p>
            <p className="text-sm text-gray-500">
              Service confirmed as completed by buyer
            </p>
          </div>
        </li>
        {/* Seller Marked as Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Seller Marked as Completed
            </h4>
            <p className="text-sm text-gray-500">Apr 25, 2023 • 16:05 PM</p>
            <p className="text-sm text-gray-500">
              Service provider marked the service as completed
            </p>
          </div>
        </li>
        {/* Service Booked */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
            <div className="w-px h-full bg-gray-300"></div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">
              Service Booked (Confirmed)
            </h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:34 PM</p>
            <p className="text-sm text-gray-500">
              Service appointment confirmed and scheduled
            </p>
          </div>
        </li>
        {/* Payment Completed */}
        <li className="flex">
          <div className="flex flex-col items-center mr-4">
            <div>
              <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold text-gray-800">Payment Completed</h4>
            <p className="text-sm text-gray-500">Apr 18, 2023 • 14:32 PM</p>
            <p className="text-sm text-gray-500">
              Payment processed successfully
            </p>
          </div>
        </li>
      </ul>
    </div>

    {/* Rate Buyer */}
    <div className="bg-white rounded-lg p-6">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Rate Buyer</h2>
      <p className="text-sm text-gray-600 mb-2">
        How was your delivery experience with Larry Brown?
      </p>
      <div className="flex space-x-1 mb-4">
        {[...Array(5)].map((_, i) => (
          <StarIcon key={i} className="w-6 h-6 text-gray-300" />
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">Quick Tags</p>
      <div className="flex flex-wrap gap-2 mb-4">
        {[
          "Kind",
          "Professional",
          "Polite & Respectful",
          "Friendly",
          "Prompt Payment",
        ].map((tag) => (
          <button
            key={tag}
            className="px-3 py-1 text-sm border rounded-full hover:bg-gray-100"
          >
            {tag}
          </button>
        ))}
      </div>
      <p className="text-sm font-medium text-gray-700 mb-2">
        Additional Comments
      </p>
      <textarea
        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
        rows={3}
        placeholder="Share your experience with the delivery service..."
      ></textarea>
      <div className="flex justify-end mt-4">
        <button className="px-6 py-2 bg-gray-800 text-white rounded-md">
          Submit Rating
        </button>
      </div>
    </div>
  </>
);

const MemberTransactionDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [view, setView] = useState("buyer"); // 'buyer', 'seller', 'service', 'service_provider'
  const [isSellerCompleted, setIsSellerCompleted] = useState(true);

  // Parse transaction ID for API calls with proper validation
  const transactionId = React.useMemo(() => {
    const parsedId = parseInt(id || "0");
    return isNaN(parsedId) ? 0 : parsedId;
  }, [id]);

  // Fetch transaction details from API
  // COMMENTED OUT FOR DEBUGGING - UNCOMMENT ONE BY ONE
  // const {
  //   data: transactionResponse,
  //   isLoading: transactionLoading,
  //   error: transactionError,
  //   refetch: refetchTransaction,
  // } = useTransactionQuery(transactionId);

  // Mock data for debugging
  const transactionResponse = {
    data: {
      id: transactionId,
      referenceId: `TRX-${transactionId}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      type: "Purchase",
      status: "completed",
      listing: "Mock Item",
      description: "Mock item description",
      amount: "eBa$ 100.00",
      counterparty: "Mock User",
      fee: "$1.00",
      net_received_paid: "eBa$ 99.00",
    },
  };
  const transactionLoading = false;
  const transactionError = null;
  const refetchTransaction = () => console.log("refetchTransaction called");

  // Extract transaction data and map to component interface
  const transactionDetails: ITransactionDetails | null =
    transactionResponse?.data
      ? {
          id: transactionResponse.data.id?.toString() || id || "0",
          transactionId:
            transactionResponse.data.referenceId ||
            `TRX-${transactionResponse.data.id}`,
          date: new Date(transactionResponse.data.createdAt).toLocaleDateString(
            "en-US",
            {
              year: "numeric",
              month: "short",
              day: "numeric",
            }
          ),
          time: new Date(transactionResponse.data.createdAt).toLocaleTimeString(
            "en-US",
            {
              hour: "2-digit",
              minute: "2-digit",
              timeZoneName: "short",
            }
          ),
          type: transactionResponse.data.type || "Purchase",
          status: transactionResponse.data.status || "Pending",
          itemName: transactionResponse.data.listing || "Item",
          itemDescription: transactionResponse.data.description || "",
          amountPaid: transactionResponse.data.amount || "eBa$ 0.00",
          amountPaidUSD: transactionResponse.data.amount || "$0.00 USD",
          buyerName: transactionResponse.data.counterparty || "Unknown Buyer",
          buyerLocation: "Location not available",
          buyerMemberSince: "Unknown",
          buyerVerified: true,
          sellerName: transactionResponse.data.counterparty || "Unknown Seller",
          sellerLocation: "Location not available",
          sellerType: "Seller",
          sellerVerified: true,
          ebaAmount: transactionResponse.data.amount || "eBa$ 0.00",
          usdFee: transactionResponse.data.fee || "$0.00",
          totalPaid: transactionResponse.data.net_received_paid || "eBa$ 0.00",
          totalPaidUSD:
            transactionResponse.data.net_received_paid || "$0.00 USD",
          deliveryStatus:
            transactionResponse.data.status === "completed"
              ? "Delivered"
              : "Pending",
          deliveryDate: transactionResponse.data.updatedAt
            ? new Date(transactionResponse.data.updatedAt).toLocaleDateString(
                "en-US",
                {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                }
              )
            : "Not available",
          shippingMethod: "eBa Delivery",
          shippingDate: transactionResponse.data.createdAt
            ? new Date(transactionResponse.data.createdAt).toLocaleDateString(
                "en-US",
                {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                }
              )
            : "Not available",
          estimatedDelivery: "2-3 business days",
          actualDelivery:
            transactionResponse.data.status === "completed"
              ? new Date(transactionResponse.data.updatedAt).toLocaleDateString(
                  "en-US",
                  {
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  }
                )
              : "Not available",
          trackingNumber: `eBa-${transactionResponse.data.id}-TRK`,
          confirmationCode: "Available after pickup",
          agentName: "To be assigned",
          agentPhone: "Available after assignment",
          vehicleNumber: "Available after assignment",
          deliveryAddress: "Delivery address on file",
        }
      : null;

  const loading = transactionLoading;

  // Determine view type based on transaction data
  // This could be enhanced to check user role vs transaction role
  // React.useEffect(() => {
  //   if (transactionDetails) {
  //     // For now, default to buyer view
  //     // In a real implementation, you would check if the current user is the buyer or seller
  //     // based on user ID comparison with transaction data
  //     if (transactionDetails.type === "Sale") {
  //       setView("seller");
  //     } else if (
  //       transactionDetails.itemName?.toLowerCase().includes("service") ||
  //       transactionDetails.itemName?.toLowerCase().includes("consultation")
  //     ) {
  //       setView("service");
  //     } else {
  //       setView("buyer");
  //     }
  //   }
  // }, [transactionDetails]);

  // Only fetch pickup code if transactionId is valid
  // COMMENTED OUT FOR DEBUGGING - UNCOMMENT ONE BY ONE
  // const {
  //   data: pickupCodeData,
  //   isLoading: pickupCodeLoading,
  //   error: pickupCodeError,
  //   refetch: refetchPickupCode,
  // } = usePickupCodeQuery(transactionId);

  // Mock data for debugging
  const pickupCodeData = null;
  const pickupCodeLoading = false;
  const pickupCodeError = null;
  const refetchPickupCode = () => console.log("refetchPickupCode called");

  // Only fetch delivery code if transactionId is valid
  // COMMENTED OUT FOR DEBUGGING - UNCOMMENT ONE BY ONE
  // const {
  //   data: deliveryCodeData,
  //   isLoading: deliveryCodeLoading,
  //   error: deliveryCodeError,
  //   refetch: refetchDeliveryCode,
  // } = useDeliveryCodeQuery(transactionId);

  // Mock data for debugging
  const deliveryCodeData = null;
  const deliveryCodeLoading = false;
  const deliveryCodeError = null;
  const refetchDeliveryCode = () => console.log("refetchDeliveryCode called");

  // Handle error states
  if (transactionError) {
    console.error("Transaction error:", transactionError);
    return (
      <MemberWrapper>
        <div className="flex justify-center items-center h-screen">
          <div className="text-center">
            <p className="text-red-500 mb-4">
              Error loading transaction details
            </p>
            <button
              onClick={() => refetchTransaction()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Handle pickup code errors (non-blocking)
  if (pickupCodeError) {
    console.error("Pickup code error:", pickupCodeError);
  }

  // Handle delivery code errors (non-blocking)
  if (deliveryCodeError) {
    console.error("Delivery code error:", deliveryCodeError);
  }

  // Handle invalid transaction ID
  if (!transactionId || transactionId === 0) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#0F2C59] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">
              Invalid transaction ID
            </div>
            <button
              onClick={() => navigate("/member/transactions")}
              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Back to Transactions
            </button>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Define renderView function after all variables are available
  const renderView = () => {
    // Ensure transactionDetails exists before rendering
    if (!transactionDetails) {
      return (
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      );
    }

    switch (view) {
      case "seller":
        return (
          <SellerView
            transactionDetails={transactionDetails}
            pickupCodeData={pickupCodeData}
            pickupCodeLoading={pickupCodeLoading}
            pickupCodeError={pickupCodeError}
            refetchPickupCode={refetchPickupCode}
          />
        );
      case "service_provider":
        return <ServiceProviderView transactionDetails={transactionDetails} />;
      case "service":
        return (
          <ServiceView
            transactionDetails={transactionDetails}
            isSellerCompleted={isSellerCompleted}
            onMarkAsCompleted={() => setIsSellerCompleted(false)}
          />
        );
      case "buyer":
      default:
        return (
          <>
            {/* Buyer and Seller Details */}
            <div className="grid grid-cols-2 gap-6 mb-6">
              {/* Buyer Details */}
              <div className="bg-white rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Buyer Details
                </h3>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <svg
                      className="w-5 h-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {transactionDetails.buyerName}
                    </p>
                    <p className="text-sm text-gray-600">
                      {transactionDetails.buyerLocation}
                    </p>
                    <p className="text-sm text-gray-600">
                      Member since {transactionDetails.buyerMemberSince}
                      {transactionDetails.buyerVerified && (
                        <span className="text-green-600 ml-2">Verified</span>
                      )}
                    </p>
                  </div>
                </div>
              </div>

              {/* Seller Details */}
              <div className="bg-white rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Seller Details
                </h3>
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <svg
                      className="w-5 h-5 text-orange-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {transactionDetails.sellerName}
                    </p>
                    <p className="text-sm text-gray-600">
                      {transactionDetails.sellerLocation}
                    </p>
                    <p className="text-sm text-gray-600">
                      {transactionDetails.sellerType}
                      {transactionDetails.sellerVerified && (
                        <span className="text-green-600 ml-2">Verified</span>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Total Paid */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Total Paid
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">eBa Amount</span>
                  <span className="font-semibold text-gray-900">
                    {transactionDetails.ebaAmount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">USD Fee (1%)</span>
                  <span className="font-semibold text-gray-900">
                    {transactionDetails.usdFee}
                  </span>
                </div>
                <hr className="my-3" />
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-900">
                    Total Paid
                  </span>
                  <div className="text-right">
                    <p className="font-bold text-gray-900">
                      {transactionDetails.totalPaid}
                    </p>
                    <p className="text-sm text-gray-500">
                      = {transactionDetails.totalPaidUSD}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Confirmation Code */}
            <div className="mb-6">
              <ConfirmationCodeCard
                type="delivery"
                data={deliveryCodeData}
                isLoading={deliveryCodeLoading}
                error={deliveryCodeError}
                onRefresh={refetchDeliveryCode}
              />
            </div>

            {/* Delivery Status Tracking */}
            <div className="mb-6">
              <DeliveryStatusTracker
                taskId={
                  (transactionDetails as any).deliveryTaskId || transactionId
                }
                currentStatus={transactionDetails.deliveryStatus || "assigned"}
                isDeliveryAgent={false}
                onStatusUpdate={(newStatus) => {
                  console.log("Delivery status updated:", newStatus);
                  // Optionally refetch transaction data here
                }}
              />
            </div>

            {/* Request Refund Section */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Request Refund
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent">
                    <option>Select a reason</option>
                    <option>Item not as described</option>
                    <option>Item damaged</option>
                    <option>Item not received</option>
                    <option>Wrong item received</option>
                    <option>Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                    rows={4}
                    placeholder="Enter details here"
                  ></textarea>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Supporting Evidence
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <svg
                      className="w-8 h-8 text-gray-400 mx-auto mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                    <p className="text-sm text-gray-500">
                      Drag files here or click to upload
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      PNG, JPG, PDF up to 10MB
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                    Request Refund
                  </button>
                </div>
              </div>
            </div>

            {/* Transaction Timeline */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Transaction Timeline
              </h3>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-3 h-3 bg-green-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-gray-900">
                        Item Delivered
                      </h4>
                      <span className="text-sm text-gray-500">
                        Apr 22, 2023 • 11:23 AM
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Package was delivered and signed for by recipient
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-gray-900">
                        Shipping in Progress
                      </h4>
                      <span className="text-sm text-gray-500">
                        Apr 19, 2023 • 08:45 AM
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Package picked up by carrier
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-gray-900">
                        Item Shipped
                      </h4>
                      <span className="text-sm text-gray-500">
                        Apr 19, 2023 • 08:32 AM
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Seller has shipped the item
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-3 h-3 bg-red-500 rounded-full mt-1 mr-4 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-gray-900">
                        Payment Completed
                      </h4>
                      <span className="text-sm text-gray-500">
                        Apr 18, 2023 • 14:32 PM
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Payment processed successfully
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Rate Seller */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Rate Seller
              </h3>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-700 mb-3">
                    How was your delivery experience with Larry Brown?
                  </p>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        className="text-gray-300 hover:text-yellow-400 text-2xl"
                      >
                        ⭐
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700 mb-3">
                    Quick Tags
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {[
                      "On Time",
                      "Professional",
                      "Friendly Seller",
                      "Fast",
                      "Item as Described",
                    ].map((tag) => (
                      <button
                        key={tag}
                        className="px-3 py-1 text-xs border border-gray-300 rounded-full text-gray-700 hover:bg-gray-50"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Comments
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                    rows={3}
                    placeholder="Share your experience with the delivery service..."
                  ></textarea>
                </div>

                <div className="flex justify-end">
                  <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                    Submit Rating
                  </button>
                </div>
              </div>
            </div>

            {/* Rate Delivery Service */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Rate Delivery Service
              </h3>

              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-700 mb-3">
                    How was your delivery experience with Larry Brown?
                  </p>
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        className="text-gray-300 hover:text-yellow-400 text-2xl"
                      >
                        ⭐
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700 mb-3">
                    Quick Tags
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {[
                      "On Time",
                      "Professional",
                      "Careful with Package",
                      "Friendly",
                      "Good Communication",
                    ].map((tag) => (
                      <button
                        key={tag}
                        className="px-3 py-1 text-xs border border-gray-300 rounded-full text-gray-700 hover:bg-gray-50"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Comments
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent resize-none"
                    rows={3}
                    placeholder="Share your experience with the delivery service..."
                  ></textarea>
                </div>

                <div className="flex justify-end">
                  <button className="px-6 py-2 bg-[#0F2C59] text-white rounded-md hover:bg-blue-700 text-sm font-medium">
                    Submit Rating
                  </button>
                </div>
              </div>
            </div>
          </>
        );
    }
  };

  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </MemberWrapper>
    );
  }

  if (!transactionDetails) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#0F2C59] min-h-screen">
          <div className="text-center py-12">
            <div className="text-white text-lg mb-2">Transaction not found</div>
            <button
              onClick={() => navigate("/member/transactions")}
              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Back to Transactions
            </button>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  return (
    <MemberWrapper>
      <div className="p-6 bg-[#0D3166] min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => navigate("/member/transactions")}
            className="text-white hover:text-gray-300 flex items-center text-lg"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Transaction Details
          </button>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setView("buyer")}
              className={`px-3 py-1 rounded-md text-sm ${view === "buyer" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Buyer View
            </button>
            <button
              onClick={() => setView("seller")}
              className={`px-3 py-1 rounded-md text-sm ${view === "seller" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Seller View
            </button>
            <button
              onClick={() => setView("service")}
              className={`px-3 py-1 rounded-md text-sm ${view === "service" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Service Recipient View
            </button>
            <button
              onClick={() => setView("service_provider")}
              className={`px-3 py-1 rounded-md text-sm ${view === "service_provider" ? "bg-white text-blue-900" : "text-white"}`}
            >
              Service Provider View
            </button>
          </div>
        </div>

        {transactionDetails && (
          <>
            {/* Transaction Summary */}
            <div className="bg-white rounded-lg p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Transaction Summary
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div>
                  <p className="text-sm text-gray-500">Transaction ID</p>
                  <p className="font-semibold text-gray-800">
                    #{transactionDetails.transactionId}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Date & Time</p>
                  <p className="font-semibold text-gray-800">
                    {transactionDetails.date}
                  </p>
                  <p className="text-sm text-gray-500">
                    {transactionDetails.time}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <p className="font-semibold text-gray-800">
                    {transactionDetails.type}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      transactionDetails.status === "Completed"
                        ? "bg-green-100 text-green-800"
                        : transactionDetails.status === "Pending Confirmation"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {transactionDetails.status}
                  </span>
                </div>
              </div>

              {/* Item Details */}
              <div className="flex items-center bg-gray-50 rounded-lg p-4">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                  <svg
                    className="w-8 h-8 text-gray-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800">
                    {transactionDetails.itemName}
                  </h3>
                  {view === "service_provider" && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2">
                      Service
                    </span>
                  )}
                  <p className="text-sm text-gray-600">
                    {transactionDetails.itemDescription}
                  </p>
                  <button className="text-sm text-red-500 hover:underline mt-1">
                    View service details
                  </button>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">Amount Paid</p>
                  <p className="text-xl font-bold text-gray-800">
                    {transactionDetails.amountPaid}
                  </p>
                  <p className="text-sm text-gray-500">
                    ≈ {transactionDetails.amountPaidUSD}
                  </p>
                </div>
              </div>
            </div>
            {renderView()}
          </>
        )}
      </div>
    </MemberWrapper>
  );
};

export default MemberTransactionDetailsPage;

// Also export as MemberViewTransactionPage for backward compatibility
export { MemberTransactionDetailsPage as MemberViewTransactionPage };
