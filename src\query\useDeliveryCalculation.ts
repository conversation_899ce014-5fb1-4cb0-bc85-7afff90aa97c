import { useMutation, useQuery } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";

// Types for delivery calculation
export interface IDeliveryCalculationRequest {
  listingId: number;
  deliveryAddress: string;
  weightKg?: number;
  lengthCm?: number;
  widthCm?: number;
  heightCm?: number;
}

export interface IDeliveryCalculationResponse {
  listingId: number;
  pickupAddress: string;
  deliveryAddress: string;
  packageDetails: {
    weightKg: number;
    dimensions: {
      lengthCm: number;
      widthCm: number;
      heightCm: number;
      volumeCm3: number;
    };
  };
  delivery: {
    success: boolean;
    distance: {
      success: boolean;
      distanceKm: number;
      origin: string;
      destination: string;
      estimatedDuration: number;
    };
    fee: {
      success: boolean;
      totalFee: number;
      breakdown: {
        baseFee: number;
        distanceFee: number;
        weightFee: number;
        volumeFee: number;
      };
      details: {
        distanceKm: number;
        weightKg: number;
        volumeCm3: number;
      };
    };
    deadline: {
      success: boolean;
      deadline: string;
      deliveryHours: number;
      distanceKm: number;
      formattedDeadline: string;
    };
    summary: {
      totalFee: number;
      deliveryDeadline: string;
      estimatedHours: number;
      distanceKm: number;
    };
  };
  estimatedCost: {
    deliveryFee: number;
    itemPrice: number;
    totalBeforePlatformFees: number;
  };
}

export interface IPlatformFeeRequest {
  listingId: number;
  saleAmount: number;
  exchangeRate?: number;
}

export interface IPlatformFeeResponse {
  success: boolean;
  transaction: {
    amount: number;
    currency: string;
    exchangeRate: number;
  };
  seller: {
    success: boolean;
    grossAmount: number;
    feeAmount: number;
    netAmount: number;
    feeRate: number;
    discountApplied?: string;
    currency: string;
    breakdown: {
      commissionRate: number;
      commissionAmount: number;
    };
  };
  buyer: {
    success: boolean;
    purchaseAmountEba: number;
    purchaseAmountUsd: number;
    feeAmountUsd: number;
    totalAmountUsd: number;
    feeRate: number;
    discountApplied?: string;
    exchangeRate: number;
    breakdown: {
      transactionRate: number;
      transactionFeeUsd: number;
    };
  };
  platform: {
    revenueEba: number;
    revenueUsd: number;
    totalRevenueUsd: number;
  };
  summary: {
    sellerReceives: number;
    buyerPays: number;
    platformEarns: number;
  };
}

export interface IDeliveryStatusUpdate {
  status: "assigned" | "picked_up" | "in_transit" | "delivered";
  location?: string;
  notes?: string;
}

export interface IDeliveryStatusHistory {
  type: string;
  message: string;
  status: string;
  location?: string;
  timestamp: string;
  loggedBy: string;
}

// Hook for calculating delivery fees and deadlines
export const useDeliveryCalculationMutation = () => {
  const { sdk } = useSDK();

  return useMutation({
    mutationFn: async (data: IDeliveryCalculationRequest) => {
      const response = await sdk.request({
        endpoint:
          "/v2/api/ebadollar/custom/member/marketplace/calculate-delivery",
        method: "POST",
        body: data,
      });
      return response;
    },
  });
};

// Hook for calculating platform fees
export const usePlatformFeeCalculationMutation = () => {
  const { sdk } = useSDK();

  return useMutation({
    mutationFn: async (data: IPlatformFeeRequest) => {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/marketplace/calculate-fees",
        method: "POST",
        body: data,
      });
      return response;
    },
  });
};

// Hook for updating delivery status
export const useDeliveryStatusUpdateMutation = () => {
  const { sdk } = useSDK();

  return useMutation({
    mutationFn: async ({
      taskId,
      update,
    }: {
      taskId: number;
      update: IDeliveryStatusUpdate;
    }) => {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/delivery-tasks/${taskId}/status`,
        method: "PUT",
        body: update,
      });
      return response;
    },
  });
};

// Hook for fetching delivery status history
export const useDeliveryStatusHistoryQuery = (
  taskId: number,
  enabled: boolean = false // DISABLED FOR DEBUGGING - CHANGE TO true TO TEST
) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["deliveryStatusHistory", taskId],
    queryFn: async () => {
      console.log("🚀 Fetching delivery status history for taskId:", taskId);
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/delivery-tasks/${taskId}/status-history`,
        method: "GET",
      });
      console.log("📡 Delivery Status History API Response:", response);
      return response.data.statusHistory;
    },
    enabled: enabled && !!taskId,
  });
};

// Helper function to format delivery status for display
export const formatDeliveryStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    assigned: "Assigned",
    picked_up: "Picked Up",
    in_transit: "In Transit",
    delivered: "Delivered",
    available: "Available",
    cancelled: "Cancelled",
  };
  return statusMap[status] || status;
};

// Helper function to get status color
export const getDeliveryStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    assigned: "text-blue-600 bg-blue-100",
    picked_up: "text-yellow-600 bg-yellow-100",
    in_transit: "text-purple-600 bg-purple-100",
    delivered: "text-green-600 bg-green-100",
    available: "text-gray-600 bg-gray-100",
    cancelled: "text-red-600 bg-red-100",
  };
  return colorMap[status] || "text-gray-600 bg-gray-100";
};

// Helper function to calculate estimated delivery time
export const calculateEstimatedDelivery = (
  distanceKm: number,
  startTime?: Date
): Date => {
  const hoursPerKm = 2;
  const deliveryHours = distanceKm * hoursPerKm;
  const start = startTime || new Date();
  return new Date(start.getTime() + deliveryHours * 60 * 60 * 1000);
};

// Helper function to format currency
export const formatCurrency = (
  amount: number,
  currency: string = "eBa$"
): string => {
  if (currency === "USD") {
    return `$${amount.toFixed(2)}`;
  }
  return `${currency} ${amount.toFixed(2)}`;
};

// Helper function to validate delivery address
export const validateDeliveryAddress = (address: string): boolean => {
  return address.trim().length >= 10; // Minimum address length
};

// Helper function to calculate package volume
export const calculatePackageVolume = (
  lengthCm: number,
  widthCm: number,
  heightCm: number
): number => {
  return lengthCm * widthCm * heightCm;
};

// Helper function to get next valid status
export const getNextValidStatus = (currentStatus: string): string[] => {
  const statusTransitions: Record<string, string[]> = {
    assigned: ["picked_up"],
    picked_up: ["in_transit"],
    in_transit: ["delivered"],
    delivered: [],
  };
  return statusTransitions[currentStatus] || [];
};

// Helper function to check if status update is allowed
export const canUpdateStatus = (
  currentStatus: string,
  newStatus: string
): boolean => {
  const validNext = getNextValidStatus(currentStatus);
  return validNext.includes(newStatus);
};

export default {
  useDeliveryCalculationMutation,
  usePlatformFeeCalculationMutation,
  useDeliveryStatusUpdateMutation,
  useDeliveryStatusHistoryQuery,
  formatDeliveryStatus,
  getDeliveryStatusColor,
  calculateEstimatedDelivery,
  formatCurrency,
  validateDeliveryAddress,
  calculatePackageVolume,
  getNextValidStatus,
  canUpdateStatus,
};
